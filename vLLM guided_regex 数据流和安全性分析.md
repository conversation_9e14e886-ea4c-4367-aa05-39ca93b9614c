崩溃trace如下

```python
INFO 06-18 11:26:09 [logger.py:42] Received request chatcmpl-4cbfe2b0583f47758093553867af8e89: prompt: '<|im_start|>system\nYou are a helpful assistant.<|im_end|>\n<|im_start|>user\nGenerate text<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.05, temperature=0.01, top_p=1.0, top_k=0, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=200, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=GuidedDecodingParams(json=None, regex='(a+)++(b+)++(c+)++d', choice=None, grammar=None, json_object=None, backend=None, backend_was_auto=False, disable_fallback=False, disable_any_whitespace=False, disable_additional_properties=False, whitespace_pattern=None, structural_tag=None), extra_args=None), prompt_token_ids: None, prompt_embeds shape: None, lora_request: None, prompt_adapter_request: None.
INFO 06-18 11:26:09 [async_llm.py:261] Added request chatcmpl-4cbfe2b0583f47758093553867af8e89.
ERROR 06-18 11:26:09 [core.py:502] EngineCore encountered a fatal error.
ERROR 06-18 11:26:09 [core.py:502] Traceback (most recent call last):
ERROR 06-18 11:26:09 [core.py:502]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 493, in run_engine_core
ERROR 06-18 11:26:09 [core.py:502]     engine_core.run_busy_loop()
ERROR 06-18 11:26:09 [core.py:502]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 520, in run_busy_loop
ERROR 06-18 11:26:09 [core.py:502]     self._process_engine_step()
ERROR 06-18 11:26:09 [core.py:502]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 545, in _process_engine_step
ERROR 06-18 11:26:09 [core.py:502]     outputs = self.step_fn()
ERROR 06-18 11:26:09 [core.py:502]               ^^^^^^^^^^^^^^
ERROR 06-18 11:26:09 [core.py:502]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 225, in step
ERROR 06-18 11:26:09 [core.py:502]     scheduler_output = self.scheduler.schedule()
ERROR 06-18 11:26:09 [core.py:502]                        ^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-18 11:26:09 [core.py:502]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/core/sched/scheduler.py", line 336, in schedule
ERROR 06-18 11:26:09 [core.py:502]     if structured_output_req and structured_output_req.grammar:
ERROR 06-18 11:26:09 [core.py:502]                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-18 11:26:09 [core.py:502]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/structured_output/request.py", line 44, in grammar
ERROR 06-18 11:26:09 [core.py:502]     completed = self._check_grammar_completion()
ERROR 06-18 11:26:09 [core.py:502]                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-18 11:26:09 [core.py:502]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/structured_output/request.py", line 32, in _check_grammar_completion
ERROR 06-18 11:26:09 [core.py:502]     self._grammar = self._grammar.result(timeout=0.0001)
ERROR 06-18 11:26:09 [core.py:502]                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-18 11:26:09 [core.py:502]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/concurrent/futures/_base.py", line 456, in result
ERROR 06-18 11:26:09 [core.py:502]     return self.__get_result()
ERROR 06-18 11:26:09 [core.py:502]            ^^^^^^^^^^^^^^^^^^^
ERROR 06-18 11:26:09 [core.py:502]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
ERROR 06-18 11:26:09 [core.py:502]     raise self._exception
ERROR 06-18 11:26:09 [core.py:502]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/concurrent/futures/thread.py", line 58, in run
ERROR 06-18 11:26:09 [core.py:502]     result = self.fn(*self.args, **self.kwargs)
ERROR 06-18 11:26:09 [core.py:502]              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-18 11:26:09 [core.py:502]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/structured_output/__init__.py", line 106, in _async_create_grammar
ERROR 06-18 11:26:09 [core.py:502]     return self.backend.compile_grammar(request_type, grammar_spec)
ERROR 06-18 11:26:09 [core.py:502]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-18 11:26:09 [core.py:502]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/structured_output/backend_xgrammar.py", line 100, in compile_grammar
ERROR 06-18 11:26:09 [core.py:502]     ctx = self.compiler.compile_regex(grammar_spec)
ERROR 06-18 11:26:09 [core.py:502]           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-18 11:26:09 [core.py:502]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/xgrammar/compiler.py", line 150, in compile_regex
ERROR 06-18 11:26:09 [core.py:502]     return CompiledGrammar._create_from_handle(self._handle.compile_regex(regex))
ERROR 06-18 11:26:09 [core.py:502]                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-18 11:26:09 [core.py:502] RuntimeError: [11:26:09] /project/cpp/regex_converter.cc:73: Regex parsing error at position 6: Two consecutive repetition modifiers are not allowed.
ERROR 06-18 11:26:09 [core.py:502] 
Process EngineCore_0:
ERROR 06-18 11:26:09 [async_llm.py:408] AsyncLLM output_handler failed.
ERROR 06-18 11:26:09 [async_llm.py:408] Traceback (most recent call last):
ERROR 06-18 11:26:09 [async_llm.py:408]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/engine/async_llm.py", line 366, in output_handler
ERROR 06-18 11:26:09 [async_llm.py:408]     outputs = await engine_core.get_output_async()
ERROR 06-18 11:26:09 [async_llm.py:408]               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-18 11:26:09 [async_llm.py:408]   File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/engine/core_client.py", line 806, in get_output_async
ERROR 06-18 11:26:09 [async_llm.py:408]     raise self._format_exception(outputs) from None
ERROR 06-18 11:26:09 [async_llm.py:408] vllm.v1.engine.exceptions.EngineDeadError: EngineCore encountered an issue. See stack trace (above) for the root cause.
Traceback (most recent call last):
INFO 06-18 11:26:09 [async_llm.py:333] Request chatcmpl-4cbfe2b0583f47758093553867af8e89 failed (engine dead).
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 504, in run_engine_core
    raise e
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 493, in run_engine_core
    engine_core.run_busy_loop()
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 520, in run_busy_loop
    self._process_engine_step()
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 545, in _process_engine_step
    outputs = self.step_fn()
              ^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 225, in step
    scheduler_output = self.scheduler.schedule()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/core/sched/scheduler.py", line 336, in schedule
    if structured_output_req and structured_output_req.grammar:
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/structured_output/request.py", line 44, in grammar
    completed = self._check_grammar_completion()
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/structured_output/request.py", line 32, in _check_grammar_completion
    self._grammar = self._grammar.result(timeout=0.0001)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/concurrent/futures/_base.py", line 456, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/structured_output/__init__.py", line 106, in _async_create_grammar
    return self.backend.compile_grammar(request_type, grammar_spec)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/vllm/v1/structured_output/backend_xgrammar.py", line 100, in compile_grammar
    ctx = self.compiler.compile_regex(grammar_spec)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/vllm/lib/python3.12/site-packages/xgrammar/compiler.py", line 150, in compile_regex
    return CompiledGrammar._create_from_handle(self._handle.compile_regex(regex))
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: [11:26:09] /project/cpp/regex_converter.cc:73: Regex parsing error at position 6: Two consecutive repetition modifiers are not allowed.

INFO:     114.215.250.108:64364 - "POST /v1/chat/completions HTTP/1.1" 500 Internal Server Error
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [794128]
```

[此处为语雀卡片，点击链接查看](https://yuque.antfin.com/docs/456070231#a544d)



<h2 id="mCkm8">概述</h2>
本文档详细分析vLLM中`guided_regex`参数的完整处理流程，包括数据流、污点流、引擎解析、异步处理机制以及安全性评估。

<h2 id="fJSrA">1. API接收层 - OpenAI协议解析</h2>
<h3 id="gy6nS">1.1 协议定义</h3>
```python
guided_regex: Optional[str] = Field(
    default=None,
    description=(
        "If specified, the output will follow the regex pattern."),
)
```

<h3 id="m4jy5">1.2 输入验证</h3>
在`protocol.py`中，`guided_regex`参数经过以下验证，这个是在转换vLLM 使用 Pydantic 解析和验证请求体的那个瞬间，并且是在数据被正式转换成 ChatCompletionRequest 或 CompletionRequest 对象之前。

```python
<augment_code_snippet path="vllm/entrypoints/openai/protocol.py" mode="EXCERPT">
@model_validator(mode="before")
@classmethod
def check_guided_decoding_count(cls, data):
    guide_count = sum([
        "guided_json" in data and data["guided_json"] is not None,
        "guided_regex" in data and data["guided_regex"] is not None,
        "guided_choice" in data and data["guided_choice"] is not None
    ])
    if guide_count > 1:
        raise ValueError(
            "You can only use one kind of guided decoding "
            "('guided_json', 'guided_regex' or 'guided_choice').")
```

**安全性问题**: 此阶段仅验证参数互斥性，**没有对正则表达式内容进行任何验证**。

<h2 id="Qs9kb">2. 参数转换层 - SamplingParams创建</h2>
根据我们先前的请求数据流分析，当执行**to_sampling_params**的时候我们就会调用到下面的流程。

<h3 id="sHBXY">2.1 GuidedDecodingParams创建</h3>
```python
<augment_code_snippet path="vllm/entrypoints/openai/protocol.py" mode="EXCERPT">
guided_decoding = GuidedDecodingParams.from_optional(
    json=self._get_guided_json_from_tool() or self.guided_json,
    regex=self.guided_regex,  # 直接传递，无验证
    choice=self.guided_choice,
    grammar=self.guided_grammar,
    json_object=guided_json_object,
    backend=self.guided_decoding_backend,
    whitespace_pattern=self.guided_whitespace_pattern,
    structural_tag=self.structural_tag,
)
```

<h3 id="XHJIP">2.2 SamplingParams集成</h3>
```python
<augment_code_snippet path="vllm/entrypoints/openai/protocol.py" mode="EXCERPT">
return SamplingParams.from_optional(
    # ... 其他参数
    guided_decoding=guided_decoding,  # 包含未验证的regex
    # ... 其他参数
)
```

**污点流分析**: `guided_regex`作为污点数据从用户输入直接流入`GuidedDecodingParams.regex`字段，未经任何清理或验证。

SamplingParams 中就已经有了我们的数据。

<h2 id="MzIRC">3. 引擎处理层 - 异步和同步路径</h2>
<h3 id="wojbD">3.1 异步处理路径</h3>
```python
<augment_code_snippet path="vllm/engine/async_llm_engine.py" mode="EXCERPT">
if isinstance(params, SamplingParams) and \
params.guided_decoding is not None:
    # 异步构建guided decoding logits processor
    params = await build_guided_decoding_logits_processor_async(
        sampling_params=params,
        tokenizer=await self.get_tokenizer_async(lora_request),
        default_guided_backend=self.decoding_config.backend,
        reasoning_backend=self.decoding_config.reasoning_backend,
        model_config=self.model_config)
```

<h3 id="CEflx">3.2 同步处理路径</h3>
```python
add_request->_add_processed_request
    ->_create_sequence_group_with_sampling
    ->_build_logits_processors

```

```python
<augment_code_snippet path="vllm/engine/llm_engine.py" mode="EXCERPT">
def _build_logits_processors(
    self, sampling_params: SamplingParams,
    lora_request: Optional[LoRARequest]) -> SamplingParams:

    if sampling_params.guided_decoding is not None:
        # 同步构建guided decoding logits processor
        processor = get_local_guided_decoding_logits_processor(
            guided_params=guided_decoding,
            tokenizer=tokenizer,
            model_config=self.model_config,
            reasoning_backend=self.decoding_config.reasoning_backend,
        
```

<h2 id="NJlhh">4. 后端选择和处理</h2>
<h3 id="NMvBG">4.1 后端选择逻辑</h3>
默认使用xgrammar，但是也可以切换lm-format-enforcer

```python
<augment_code_snippet path="vllm/model_executor/guided_decoding/__init__.py" mode="EXCERPT">
def maybe_backend_fallback(guided_params: GuidedDecodingParams) -> GuidedDecodingParams:
    # 默认使用xgrammar后端
    if guided_params.backend == "auto":
        guided_params.backend = "xgrammar"
</augment_code_snippet>
```

<h3 id="ycNYA">4.2 Outlines后端处理</h3>
 

```python
<augment_code_snippet path="vllm/model_executor/guided_decoding/outlines_decoding.py" mode="EXCERPT">
def _get_guide_and_mode(guided_params: GuidedDecodingParams):
elif guided_params.regex:
    return guided_params.regex, GuidedDecodingMode.REGEX  # 直接返回用户输入
</augment_code_snippet>
```

<h3 id="WN48V">4.3 XGrammar后端处理</h3>
```python
<augment_code_snippet path="vllm/model_executor/guided_decoding/xgrammar_decoding.py" mode="EXCERPT">
elif guided_params.regex:
    return cls(
        regex_str=guided_params.regex,  # 直接使用用户输入
        tokenizer_hash=tokenizer_hash,
        max_threads=max_threads,
        tokenizer_data=tokenizer_data,
    )
</augment_code_snippet>
```

<h2 id="KEaoG">5. 正则表达式编译和执行</h2>
<h3 id="jj1s2">5.1 Outlines - RegexGuide编译</h3>
```python
<augment_code_snippet path="vllm/model_executor/guided_decoding/outlines_logits_processors.py" mode="EXCERPT">
@classmethod
@cache()
def _get_guide(cls, regex_string: str,
               tokenizer: PreTrainedTokenizerBase) -> Guide:
    tokenizer = _adapt_tokenizer(tokenizer)
    return RegexGuide.from_regex(regex_string, tokenizer)  # 可能抛出异常
```

<h3 id="WNkLf">5.2 XGrammar - 正则编译</h3>
```python

<augment_code_snippet path="vllm/model_executor/guided_decoding/xgrammar_decoding.py" mode="EXCERPT">
def _ensure_ctx(self):
    if self.ctx is None:
        compiler = GrammarCompilerCache.get_compiler(self.config)
        # ... 其他分支
        elif self.config.regex_str:
            self.ctx = compiler.compile_regex(self.config.regex_str)  # 可能崩溃
```

<h3 id="zftD2">5.3 正则执行</h3>
方法是 vLLM 中引导式解码（Guided Decoding）功能在模型推理阶段的核心环节。它的调用时机是在模型已经计算出下一个**词元的概率分布（即 logits），但在进行采样（sampling）**之前。

```python

@dataclass
class XGrammarLogitsProcessor:
    """Wrapper class to support pickle protocol"""
    config: GrammarConfig
    reasoner: ReasoningParser | None = None

    ctx: xgr.CompiledGrammar | None = None
    tokenizer_info: xgr.TokenizerInfo = None  # type: ignore[assignment]
    token_bitmask: torch.Tensor = None  # type: ignore[assignment]
    matchers: list[xgr.GrammarMatcher] = field(default_factory=list)
    batch_size: int = field(default=1)
    prefilled: bool = field(default=False)


    def __call__(self, input_ids: list[int],
                 scores: torch.Tensor) -> torch.Tensor:

        # Skip the structured logits processing if reasoning is not finished.
        # reasoner is not None only when `--reasoning-parser` is set.
        if self.reasoner is not None and \
        not self.reasoner.is_reasoning_end(
                input_ids):
            return scores

        if self.ctx is None:
            self._ensure_ctx()
```



<h2 id="gmTC8">6 深度分析</h2>
<h3 id="dwOVu">6.1 数据流图</h3>
```plain
用户输入 → OpenAI API → protocol.py → GuidedDecodingParams → SamplingParams
    ↓
AsyncLLMEngine/LLMEngine → guided_decoding模块 → 后端选择
    ↓
Outlines/XGrammar/Guidance → 正则编译 → LogitsProcessor → 模型推理
```

<h3 id="ObLXK">6.2 污点传播路径</h3>
1. **入口点**: `ChatCompletionRequest.guided_regex` / `CompletionRequest.guided_regex`
2. **传播路径**:
    - `protocol.py:516` → `GuidedDecodingParams.regex`
    - `protocol.py:551` → `SamplingParams.guided_decoding`
    - `async_llm_engine.py:523` → `build_guided_decoding_logits_processor_async`
    - `outlines_decoding.py:124` → `RegexLogitsProcessor._get_guide`
    - `outlines_logits_processors.py:146` → `RegexGuide.from_regex`

<h3 id="U5ZGl">6.4 缓存机制分析</h3>
<h4 id="Bfdpb">6.4.1 Outlines缓存</h4>
```python
<augment_code_snippet path="vllm/model_executor/guided_decoding/outlines_logits_processors.py" mode="EXCERPT">
@cache()  # outlines.caching.cache装饰器
def _get_guide(cls, regex_string: str, tokenizer: PreTrainedTokenizerBase) -> Guide:
    # 缓存键: (regex_string, tokenizer_hash)
    # 风险: 恶意regex被永久缓存
    </augment_code_snippet>
```

<h4 id="ECYYJ">6.4.2 XGrammar缓存</h4>
```python
<augment_code_snippet path="vllm/model_executor/guided_decoding/xgrammar_decoding.py" mode="EXCERPT">
class GrammarCompilerCache:
    _cache: dict[str, xgr.GrammarCompiler] = {}

    @classmethod
    def get_compiler(cls, config: GrammarConfig) -> xgr.GrammarCompiler:
        # 编译器级别缓存，可能保存恶意状态
        ````
        </augment_code_snippet>
```

bug 疑似在缓存内

```python

class GrammarCompilerCache:
    """
    Cache for GrammarCompiler instances based on tokenizer.

    This cache reduces the overhead of creating new compiler instances when
    using the same tokenizer configuration.
    """
    _cache: dict[str, xgr.GrammarCompiler] = {}

    @classmethod
    def get_compiler(cls, config: GrammarConfig) -> xgr.GrammarCompiler:
        cache_key = str(config.tokenizer_hash)

        if cache_key not in cls._cache:
            config_data = config.tokenizer_data

            # In TokenizerDataCache.get_tokenizer_data, a serializable
            # tokenizer_data is created and cached. This data is used to build
            # a tokenizer_info and create an xgrammar compiler.
            tokenizer_info = xgr.TokenizerInfo.from_vocab_and_metadata(
                encoded_vocab=config_data.encoded_vocab,
                metadata=config_data.metadata,
            )
            cache_size = vllm.envs.VLLM_XGRAMMAR_CACHE_MB * 1024 * 1024
            cls._cache[cache_key] = xgr.GrammarCompiler(
                tokenizer_info,
                max_threads=config.max_threads,
                cache_enabled=True,
                cache_limit_bytes=cache_size,
            )

        return cls._cache[cache_key]

```

他是每个模型内共享GrammarCompiler

<h4 id="lkKah">DEBUG调试</h4>
首先使用debugpy 启动

```python
python -Xfrozen_modules=off  -m debugpy --listen 9501 $(which vllm)  serve Qwen/Qwen2.5-VL-3B-Instruct --port 8901 --api-key tokentest --enforce-eager
```

然后修改vscode launch.json

```json
{
  "name": "sh_file_debug",
  "type": "debugpy",
  "request": "attach",
  "justMyCode": false, 
  "subProcess": true,
  "pathMappings": [
    {
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "${workspaceFolder}"
    }
  ],
  "connect": {
    "host": "localhost",
    "port": 9501
  }
}
```



![](https://intranetproxy.alipay.com/skylark/lark/0/2025/png/185656719/1750314012531-20c062a3-52ca-40ba-975d-6b6ea5f831ff.png)

调试定位在，这就是我们的漏洞点。

```python

    def compile_grammar(self, request_type: StructuredOutputOptions,
                        grammar_spec: str) -> StructuredOutputGrammar:
        if request_type == StructuredOutputOptions.JSON:
            ctx = self.compiler.compile_json_schema(
                grammar_spec, any_whitespace=not self.disable_any_whitespace)
        elif request_type == StructuredOutputOptions.JSON_OBJECT:
            ctx = self.compiler.compile_json_schema(
                '{"type": "object"}',
                any_whitespace=not self.disable_any_whitespace)
        elif request_type == StructuredOutputOptions.GRAMMAR:
            ctx = self.compiler.compile_grammar(grammar_spec)
        elif request_type == StructuredOutputOptions.REGEX:
            ctx = self.compiler.compile_regex(grammar_spec)
        elif request_type == StructuredOutputOptions.STRUCTURAL_TAG:
            s_tag = json.loads(grammar_spec)
            tags = [
                xgr.StructuralTagItem(
                    begin=s["begin"],
                    schema=json.dumps(s["schema"]),
                    end=s["end"],
                ) for s in s_tag["structures"]
            ]
            ctx = self.compiler.compile_structural_tag(tags, s_tag["triggers"])
        else:
            logger.error(
                "Validation should have already occurred. Please file an issue."
            )
            raise ValueError(
                f"grammar is not of valid supported types. ({request_type!s})")

        return XgrammarGrammar(
            matcher=xgr.GrammarMatcher(
                ctx,
                max_rollback_tokens=self.num_speculative_tokens,
            ),
            vocab_size=self.vocab_size,
            ctx=ctx,
        )
```

问题在xgrammar编译内部，因此我们切换到xgrammar进行测试

```python
 ctx = self.compiler.compile_regex(grammar_spec)
```

调试的时候发现，第一次发送

```json
  "guided_regex": "(a+)++(b+)++(c+)++d", 
```

根本不会进入compile regex的环节，同时导致后面所有的都不会走regex



![](https://intranetproxy.alipay.com/skylark/lark/0/2025/png/185656719/1750334285650-5a990153-5b86-4c63-aa4f-8ae410d9c249.png)







<h3 id="A0WSN">情况1，发送正确的，然后发送poc，触发崩溃</h3>
首次发送会进行编译

![](https://intranetproxy.alipay.com/skylark/lark/0/2025/png/185656719/1750401648726-c7829787-61b3-4a9e-b20b-aa476865e385.png)![](https://intranetproxy.alipay.com/skylark/lark/0/2025/png/185656719/1750401661875-d48656f1-8714-41fd-a507-0495d74b1945.png)

第二次发送相同正确的也会走编译

第三次发送了poc，也会走编译

<h3 id="Dfhb7">情况2，发送poc 不会触发崩溃</h3>
没有走到编译，为什么呢？

![](https://intranetproxy.alipay.com/skylark/lark/0/2025/png/185656719/1750402316886-05922e84-f20c-44e0-a0e4-73a5501fd1f6.png)



他选择了guidance 另一个backend

回溯一下 首先后端是默认的none

![](https://intranetproxy.alipay.com/skylark/lark/0/2025/png/185656719/1750403908823-19c25873-19b9-430f-8c5e-c2d9592fa234.png)



![](https://intranetproxy.alipay.com/skylark/lark/0/2025/png/185656719/1750404029837-750f2111-9a2b-4910-9690-b7b1158b2f44.png)

![](https://intranetproxy.alipay.com/skylark/lark/0/2025/png/185656719/1750404045204-059d2959-b674-4c92-b573-76ad02b15421.png)



backend 是在 启动的时候选取了auto，什么时候选取的呢？

![](https://intranetproxy.alipay.com/skylark/lark/0/2025/png/185656719/1750406251853-02f83fea-6585-4289-b145-eced0743d4c8.png)

在第一次的时候会进行编译测试。

```json
def _validate_structured_output(self, params: SamplingParams) -> None:
    if not params.guided_decoding or not self.decoding_config:
        return

    engine_level_backend = self.decoding_config.backend
    if params.guided_decoding.backend:
        # Request-level backend selection is not supported in V1.
        # The values may differ if `params` is reused and was set
        # to a specific backend based on `auto` behavior in a previous
        # request. We remember that it was set as a result of `auto`
        # using the `_auto` option set on the backend in the params.
        if (params.guided_decoding.backend != engine_level_backend
                and not (engine_level_backend == "auto"
                         and params.guided_decoding.backend_was_auto)):
            raise ValueError(...)
    else:
        params.guided_decoding.backend = engine_level_backend

    # Request content validation
    if engine_level_backend.startswith("xgrammar"):
        # xgrammar with no fallback
        validate_xgrammar_grammar(params)
    elif engine_level_backend.startswith("guidance"):
        validate_guidance_grammar(params, tokenizer=None)
    else:
        # NOTE: engine_level_backend must be "auto" here
        # "auto" is an opt-in to opinionated behavior where we try to
        # choose a backend based on request contents.
        try:
            validate_xgrammar_grammar(params)
            params.guided_decoding.backend = "xgrammar"  # 这里修改！
        except ValueError:
            # Fall back to guidance.
            validate_guidance_grammar(params, tokenizer=None)
            params.guided_decoding.backend = "guidance"  # 这里修改！
        # Remember that this backend was set automatically
        params.guided_decoding.backend_was_auto = True
```

根据调试我们终于找到关键内容了，第一次发送的时候，会因为报错，然后回退到guidance，因为缓存之后一直使用guidance

![](https://intranetproxy.alipay.com/skylark/lark/0/2025/png/185656719/1750409088398-347f8c22-7224-4840-aa8c-40e2e8809fc3.png)

这样就解释了，为什么发送第一次反而不会触发。



<h2 id="qG96N">总结</h2>
这个漏洞比较巧合，需要在使用xgrammar的情况下才能触发（虽然是默认的）， 如果第一次执行的话，触发漏洞，就会回退到guidance，导致之后无法触发。因此需要第一次正常执行，让他选择xgrammar，然后第二次利用缓存机制触发漏洞。



